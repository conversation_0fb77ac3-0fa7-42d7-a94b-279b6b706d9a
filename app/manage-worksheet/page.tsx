import { CreateWorksheet } from '@/components/organisms/MangeWorksheet/CreateWorksheet/CreateWorksheet';
import { WorksheetListing } from '@/components/organisms/MangeWorksheet/WorksheetListing/WorksheetListing';
import { WorksheetReview } from '@/components/organisms/MangeWorksheet/WorksheetReview/WorksheetReview';
import { EViewType } from '@/config/enums/enum';
export const dynamic = 'force-dynamic'; // Ensures the page is always rendered dynamically

export default async function ManageWorksheetPage({
  searchParams,
}: {
  searchParams?: Promise<{ [key: string]: string | string[] | undefined }>;
}) {
  // In Next.js 15, searchParams is a Promise that needs to be awaited
  const searchParamsValue = await searchParams;
  const viewType = searchParamsValue?.type || EViewType.LISTING;

  return (
    <div className="flex w-full h-full">
      {viewType === EViewType.LISTING && <WorksheetListing />}
      {viewType === EViewType.CREATE && <CreateWorksheet key="create-worksheet" />}
      {viewType === EViewType.REVIEW && (
        <WorksheetReview id={searchParamsValue?.id as string} />
      )}
    </div>
  );
}
