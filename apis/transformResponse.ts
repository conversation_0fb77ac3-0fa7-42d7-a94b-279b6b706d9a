type TValidationError = {
  field: string;
  constraints: string;
};

export type TApiError = {
  status: 'error';
  message: string | TValidationError[];
  statusCode?: number;
};

export type TSuccess<T> = {
  status: 'success';
  data: T;
};

export type TTransformResponse<T> = TSuccess<T> | TApiError;

export type TTransformPaginationResponse<T> = {
  data: T[];
  count: number;
  total: number;
  page: number;
  pageCount: number;
};

export const transformResponse = async <T>(response: Response): Promise<TTransformResponse<T>> => {
  try {
    // Handle 204 No Content responses (like successful DELETE operations)
    if (response.status === 204) {
      return { status: 'success', data: { message: 'Operation completed successfully' } as unknown as T };
    }
    
    const data: any = await response.json();
    if (response.ok) {
      // Log the raw data structure to help confirm where 'items' are.
      // console.log('Raw data for mapping:', JSON.stringify(data, null, 2));

      let mappedData: any;

      // Check if the response has the "meta" structure for pagination
      if (data.meta && data.items !== undefined) {
        // Assuming 'items' is a top-level array alongside 'meta'
        mappedData = {
          items: data.items,
          currentPage: data.meta.page,
          pageSize: data.meta.pageSize,
          totalItems: data.meta.total,
          totalPages: data.meta.totalPages,
        };
      } else if (data?.data !== undefined) {
        // Fallback for existing structure where data is nested under a 'data' property
        mappedData = data.data;
      } else {
        // Fallback for direct data structure (if it matches TPagination<T> directly)
        mappedData = data;
      }
      // Ensure the mappedData conforms to T, which for worksheets is TPagination<TWorksheet>
      return { status: 'success', data: mappedData as T };
    }

    const error = data as unknown as TApiError;
    if (typeof error?.message === 'string') return { status: 'error', message: error.message as any };
    return { status: 'error', message: error.message as any };
  } catch (e) {
    console.error('Error in transformResponse:', e);
    return { status: 'error', message: 'fetch error' };
  }
};
