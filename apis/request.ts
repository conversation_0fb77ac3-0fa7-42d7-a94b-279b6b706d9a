import { transformResponse } from './transformResponse';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/config/auth';

export const request = async <T>({
  url,
  options,
  noContentType = false,
}: {
  url: string;
  options?: RequestInit;
  noContentType?: boolean;
}) => {
  const baseUrl = process.env.API_URL + url;

  // Get the session to extract the accessToken
  const session = await getServerSession(authOptions);
  const accessToken = session?.user?.accessToken;

  const headers: RequestInit = {
    headers: {
      ...(noContentType ? {} : { 'Content-Type': 'application/json' }),
      ...(accessToken ? { 'Authorization': `Bearer ${accessToken}` } : {}),
      ...options?.headers,
    },
  };

  // Add debugging logs
  console.log(`Making request to: ${baseUrl}`);
  console.log(`With authentication: ${accessToken ? 'Yes' : 'No'}`);

  const response = await fetch(baseUrl, { next: { revalidate: 10 }, ...options, ...headers });

  // Log response status
  console.log(`Response status: ${response.status}`);

  return transformResponse<T>(response);
};
