import { request } from './request'; // Assuming a generic request helper
import { TTransformResponse } from './transformResponse'; // For response typing

export interface ICreateSchoolPayload {
  name: string;
  address: string;
  phoneNumber: string;
  registeredNumber: string;
  email: string;
  adminId?: string;
  brandId?: string;
}

export interface IUpdateSchoolPayload {
  name?: string;
  address?: string;
  phoneNumber?: string;
  registeredNumber?: string;
  email?: string;
}

export interface ISchoolResponse {
  id: string;
  name: string;
  address: string;
  phoneNumber: string;
  registeredNumber: string;
  email: string;
  admin?: {
    id: string;
    name: string;
    email: string;
    role: string;
  };
  brand?: {
    id: string;
    logo?: string;
    color?: string;
    image?: string;
  };
  createdAt?: string;
  updatedAt?: string;
  examinationFormat?: {
    uploadedAt?: string;
    uploader?: string;
    fileSize?: number;
  };
  narrativeStructure?: {
    createdAt?: string;
    extractedBy?: string;
    status?: string;
  };
}

export interface IAssignSchoolManagerPayload {
  adminId: string;
}

const SCHOOLS_API_ENDPOINT = '/schools'; // As per documentation

/**
 * Creates a new school.
 * Corresponds to: POST /schools
 * @param payload - The school data.
 * @returns The created school details.
 */
export async function createSchool(payload: ICreateSchoolPayload): Promise<TTransformResponse<ISchoolResponse>> {
  try {
    const response = await request<ISchoolResponse>({
      url: SCHOOLS_API_ENDPOINT,
      options: {
        method: 'POST',
        body: JSON.stringify(payload),
      },
    });
    return response;
  } catch (error: any) {
    console.error('Error creating school:', error);
    return { status: 'error', message: error.message || 'An unexpected error occurred while creating the school.' };
  }
}

/**
 * Assigns a school manager to a school (updates the school's adminId).
 * Corresponds to: PATCH /schools/{id}
 * @param schoolId - The ID of the school to update.
 * @param payload - The data containing the adminId to assign.
 * @returns The updated school details.
 */
export async function assignSchoolManager(schoolId: string, payload: IAssignSchoolManagerPayload): Promise<TTransformResponse<ISchoolResponse>> {
  try {
    const response = await request<ISchoolResponse>({
      url: `${SCHOOLS_API_ENDPOINT}/${schoolId}`,
      options: {
        method: 'PATCH',
        body: JSON.stringify(payload),
      },
    });
    return response;
  } catch (error: any) {
    console.error('Error assigning school manager:', error);
    return { status: 'error', message: error.message || 'An unexpected error occurred while assigning the school manager.' };
  }
}

/**
 * Fetches all schools.
 * Corresponds to: GET /schools
 * @returns A list of all schools.
 */
export async function getAllSchools(): Promise<TTransformResponse<ISchoolResponse[]>> {
  try {
    const response = await request<ISchoolResponse[]>({
      url: SCHOOLS_API_ENDPOINT,
      options: {
        method: 'GET',
      },
    });
    return response;
  } catch (error: any) {
    console.error('Error fetching schools:', error);
    return { status: 'error', message: error.message || 'An unexpected error occurred while fetching schools.' };
  }
}

/**
 * Fetches a school by ID.
 * Corresponds to: GET /schools/{id}
 * @param schoolId - The ID of the school to fetch.
 * @returns The school details.
 */
export async function getSchoolById(schoolId: string): Promise<TTransformResponse<ISchoolResponse>> {
  try {
    const response = await request<ISchoolResponse>({
      url: `${SCHOOLS_API_ENDPOINT}/${schoolId}`,
      options: {
        method: 'GET',
      },
    });
    return response;
  } catch (error: any) {
    console.error('Error fetching school:', error);
    return { status: 'error', message: error.message || 'An unexpected error occurred while fetching the school.' };
  }
}

/**
 * Updates an existing school's information.
 * Corresponds to: PATCH /schools/{id}
 * @param schoolId - The ID of the school to update.
 * @param payload - The school data to update.
 * @returns The updated school details.
 */
export async function updateSchool(schoolId: string, payload: IUpdateSchoolPayload): Promise<TTransformResponse<ISchoolResponse>> {
  try {
    const response = await request<ISchoolResponse>({
      url: `${SCHOOLS_API_ENDPOINT}/${schoolId}`,
      options: {
        method: 'PATCH',
        body: JSON.stringify(payload),
      },
    });
    return response;
  } catch (error: any) {
    console.error('Error updating school:', error);
    return { status: 'error', message: error.message || 'An unexpected error occurred while updating the school.' };
  }
}

/**
 * Deletes a school by ID.
 * Corresponds to: DELETE /schools/{id}
 * @param schoolId - The ID of the school to delete.
 * @returns A success or error response.
 */
export async function deleteSchool(schoolId: string): Promise<TTransformResponse<null>> {
  try {
    const response = await request<null>({
      url: `${SCHOOLS_API_ENDPOINT}/${schoolId}`,
      options: {
        method: 'DELETE',
      },
    });
    return response;
  } catch (error: any) {
    console.error('Error deleting school:', error);
    return { status: 'error', message: error.message || 'An unexpected error occurred while deleting the school.' };
  }
}
