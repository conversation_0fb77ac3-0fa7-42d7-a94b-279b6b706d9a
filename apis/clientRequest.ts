import { transformResponse } from './transformResponse';
import { TTransformResponse } from './transformResponse';

/**
 * Client-side request helper function for making API requests with authentication
 * @param url - The URL to make the request to
 * @param options - The request options
 * @param accessToken - The access token for authentication
 * @param noContentType - Whether to omit the Content-Type header (useful for FormData)
 * @returns The transformed response
 */
export const clientRequest = async <T>({
  url,
  options,
  accessToken,
  noContentType = false,
}: {
  url: string;
  options?: RequestInit;
  accessToken?: string;
  noContentType?: boolean;
}): Promise<TTransformResponse<T>> => {
  const baseUrl = process.env.NEXT_PUBLIC_API_URL || process.env.BASE_URL || '';
  const fullUrl = baseUrl + url;

  const headers: HeadersInit = {
    ...(noContentType ? {} : { 'Content-Type': 'application/json' }),
    ...(accessToken ? { 'Authorization': `Bearer ${accessToken}` } : {}),
    ...options?.headers as HeadersInit,
  };

  try {
    const response = await fetch(fullUrl, {
      ...options,
      headers,
    });

    return await transformResponse<T>(response);
  } catch (error: any) {
    console.error('Request error:', error);
    return { 
      status: 'error', 
      message: error.message || 'An unexpected error occurred while making the request.' 
    };
  }
};