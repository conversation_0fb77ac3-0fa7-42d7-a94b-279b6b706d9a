'use client';

import { ReactNode } from 'react';
import { motion } from 'framer-motion';
import Image from 'next/image';
import Icon from '@/components/atoms/Icon/Icon';
import { cn } from '@/utils/cn';
import { Edit, Calculator, Info } from 'lucide-react';
import { Button } from '@/components/atoms/Button/Button';

interface AuthTemplateProps {
  children: ReactNode;
  className?: string;
}

export default function AuthTemplate({ children, className }: AuthTemplateProps) {
  return (
    <div className="min-h-screen flex flex-col md:flex-row">
      {/* Left Section - Illustration and Branding */}
      <div className="bg-blue-100 md:w-1/2 p-8 flex flex-col justify-center items-center relative overflow-hidden space-y-8">
        {/* Logo */}
        <div className="absolute top-8 left-8 z-10">
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
          >
            <div className="flex items-center space-x-3">
              <Icon variant="logo" size={52} className="drop-shadow-sm" />
            </div>
          </motion.div>
        </div>

          {/* Main Illustration */}
          <div className="flex justify-center items-center z-10">
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <div className="max-w-md w-full h-80 bg-white/10 rounded-2xl p-4 flex items-center justify-center relative">
                <Image
                  src="/assets/sign-in.png"
                  alt="Education Illustration"
                  width={400}
                  height={320}
                  className="w-full h-full object-contain rounded-xl"
                  onError={(e) => {
                    console.error('Image failed to load:', e);
                    // Show fallback content
                    const fallback = e.currentTarget.nextElementSibling as HTMLElement;
                    if (fallback) fallback.style.display = 'flex';
                    e.currentTarget.style.display = 'none';
                  }}
                  onLoad={() => console.log('Image loaded successfully')}
                />
                {/* Fallback content */}
                <div className="absolute inset-0 hidden flex-col items-center justify-center text-center p-8">
                  <div className="w-24 h-24 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center mb-4">
                    <Icon variant="book-open" size={8} className="text-white" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-700 mb-2">Education Platform</h3>
                  <p className="text-sm text-gray-600">Empowering Singapore&apos;s Future</p>
                </div>
              </div>
            </motion.div>
          </div>

          {/* New to EduSG Section */}
          <motion.div
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.8 }}
          >
            <div className="z-50 max-w-sm mx-auto">
            <div className="relative overflow-hidden">
              {/* Gradient Background */}
              <div className="absolute inset-0 bg-gradient-to-br from-white/20 via-blue-200/30 to-indigo-200/30 rounded-2xl backdrop-blur-sm"></div>

              {/* Glass Effect */}
              <div className="relative bg-white/70 backdrop-blur-sm border border-white/40 rounded-2xl p-4 shadow-lg">
                {/* Decorative Elements */}
                <div className="absolute top-0 right-0 w-12 h-12 bg-gradient-to-br from-blue-400/20 to-purple-400/20 rounded-full -mr-6 -mt-6"></div>

                <div className="relative z-20">
                  <div className="flex items-start space-x-2.5">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center shadow-lg">
                        <Info size={16} className="text-white" />
                      </div>
                    </div>
                    <div className="flex-1">
                      <h3 className="text-sm font-semibold text-gray-800 mb-1.5">New to EduSG?</h3>
                      <p className="text-xs text-gray-700 mb-2.5 leading-relaxed">
                        We&apos;re in <span className="font-medium text-blue-600">MVP stage</span>. New accounts are created manually for the best experience.
                      </p>
                      <div className="grid gap-1.5">
                        <Button
                          href="mailto:<EMAIL>?subject=New Account Request&body=Hi, I would like to request a new EduSG account. Please provide me with login credentials."
                          variant="outline"
                          className="w-full text-xs h-8 rounded-lg border-2 border-blue-200 text-blue-700 hover:bg-blue-50 hover:border-blue-300 transition-all duration-300 focus:ring-2 focus:ring-blue-100"
                          iconProps={{ variant: 'help-circle', size: 2.5, className: 'text-blue-600' }}
                        >
                          Request New Account
                        </Button>
                        <Button
                          href="/demo"
                          variant="ghost"
                          className="w-full text-xs h-8 rounded-lg text-gray-600 hover:bg-gray-100 transition-all duration-300 focus:ring-2 focus:ring-gray-100"
                          iconProps={{ variant: 'presentation', size: 2.5, className: 'text-gray-500' }}
                        >
                          Try Demo Access
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            </div>
          </motion.div>

          {/* Background Decorative Elements */}
          <div className="absolute top-0 right-0 w-64 h-64 bg-blue-200 rounded-full -mr-32 -mt-16 opacity-50"></div>
          <div className="absolute bottom-0 left-0 w-64 h-64 bg-blue-200 rounded-full -ml-32 -mb-16 opacity-50"></div>
    
      </div>

      {/* Right Section - Content */}
      <div className={cn(
        'md:w-1/2 p-4 sm:p-6 lg:p-8 flex flex-col justify-center items-center min-h-screen overflow-y-auto',
        className
      )}>
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <div className="w-full max-w-lg">
          {children}

          {/* Singapore Education Theme */}
          <div className="mt-12 pt-6 border-t border-gray-200">
            <div className="flex justify-center space-x-6">
              <Icon variant="book-open" size={4} className="text-gray-400 hover:text-primary transition-colors" />
              <Edit size={16} className="text-gray-400 hover:text-primary transition-colors" />
              <Calculator size={16} className="text-gray-400 hover:text-primary transition-colors" />
              <Icon variant="globe" size={4} className="text-gray-400 hover:text-primary transition-colors" />
            </div>
          </div>
        </div>
        </motion.div>
      </div>
    </div>
  );
}
