'use client';

import React from 'react';
import { AlertCircle, CheckCircle2 } from 'lucide-react';
import { cn } from '@/utils/cn';

export interface FormFieldProps {
  label: string;
  error?: string;
  children: React.ReactNode;
  icon?: React.ReactNode;
  required?: boolean;
  success?: boolean;
  hint?: string;
}

export const FormField: React.FC<FormFieldProps> = ({
  label,
  error,
  children,
  icon,
  required = false,
  success = false,
  hint,
}) => {
  // Clone children to pass icon props if it's an Input component
  const childrenWithProps = React.Children.map(children, (child) => {
    if (React.isValidElement(child)) {
      // Check if it's an Input component by checking the displayName or type
      const childType = child.type as any;
      const isInputComponent = childType?.displayName === 'Input' ||
                              child.props['data-slot'] === 'input' ||
                              (childType?.name === 'Input');

      if (isInputComponent) {
        return React.cloneElement(child as React.ReactElement<any>, {
          hasLeftIcon: !!icon,
          hasRightIcon: success && !error,
        });
      }
    }
    return child;
  });

  return (
    <div className="form-control space-y-2">
      <label className="block text-sm font-medium text-gray-700">
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </label>

      <div className="relative">
        {icon && (
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
            <div className="text-gray-500">
              {icon}
            </div>
          </div>
        )}
        {childrenWithProps}
        {success && !error && (
          <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none z-10">
            <CheckCircle2 size={16} className="text-green-500" />
          </div>
        )}
      </div>

      {error && (
        <div className="flex items-start gap-2 mt-2">
          <AlertCircle size={14} className="text-red-500 mt-0.5 flex-shrink-0" />
          <p className="text-sm text-red-600 leading-tight">{error}</p>
        </div>
      )}

      {hint && !error && (
        <p className="text-xs text-gray-500 leading-tight">{hint}</p>
      )}
    </div>
  );
};
