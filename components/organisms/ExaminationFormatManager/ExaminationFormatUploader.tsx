'use client';

import React, { useState, useCallback, useRef } from 'react';
import Icon from '@/components/atoms/Icon/Icon';
import { Button } from '@/components/atoms/Button/Button';
import { AlertMessage } from '@/components/molecules/AlertMessage/AlertMessage';
import { SchoolSelector } from '@/components/molecules/FormItems/SchoolSelector';
import { uploadExaminationFormatAction } from '@/actions/examinationFormat.action';
import { fetchSchools } from '@/actions/school.action';
import { useForm, Controller } from 'react-hook-form';
import { Upload, File, X, Loader2, CheckCircle, AlertTriangle, <PERSON>rkles, Brain, Zap } from 'lucide-react';
import { ProgressBar } from '@/components/molecules/ProgressBar/ProgressBar';

interface ExaminationFormatUploaderProps {
  schoolId?: string;
  onUploadSuccess?: (response: any) => void;
  onUploadError?: (error: string) => void;
  className?: string;
  isReplacement?: boolean;
}

interface UploadFormData {
  schoolId: string;
}

export const ExaminationFormatUploader: React.FC<ExaminationFormatUploaderProps> = ({
  schoolId: initialSchoolId,
  onUploadSuccess,
  onUploadError,
  className,
  isReplacement = false,
}) => {
  const [file, setFile] = useState<File | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [validationMessage, setValidationMessage] = useState<{type: 'success' | 'error' | 'warning' | null, message: string | null}>({type: null, message: null});
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  const { control, handleSubmit, formState: { errors }, watch, setValue } = useForm<UploadFormData>({
    defaultValues: {
      schoolId: initialSchoolId || '',
    },
  });

  const watchedSchoolId = watch('schoolId');

  // Reset file when school changes
  React.useEffect(() => {
    if (initialSchoolId && initialSchoolId !== watchedSchoolId) {
      setValue('schoolId', initialSchoolId);
    }
  }, [initialSchoolId, setValue, watchedSchoolId]);

  const validateFile = (file: File): {isValid: boolean, message: string | null, type: 'success' | 'error' | 'warning' | null} => {
    // Check file type
    if (file.type !== 'application/pdf') {
      return {
        isValid: false, 
        message: 'Only PDF files are allowed', 
        type: 'error'
      };
    }
    
    // Check file size (5MB limit)
    const maxSize = 5 * 1024 * 1024; // 5MB in bytes
    if (file.size > maxSize) {
      return {
        isValid: false, 
        message: 'File size exceeds 5MB limit', 
        type: 'error'
      };
    }

    // Check file size warning (large but acceptable)
    const warningSize = 3 * 1024 * 1024; // 3MB warning
    if (file.size > warningSize) {
      return {
        isValid: true, 
        message: 'File is large (>3MB). Consider optimizing for faster uploads.', 
        type: 'warning'
      };
    }
    
    return {
      isValid: true, 
      message: 'File is valid and ready to upload', 
      type: 'success'
    };
  };

  const handleFileChange = (selectedFile: File | null) => {
    setError(null);
    setSuccess(null);
    setValidationMessage({type: null, message: null});
    
    if (!selectedFile) {
      setFile(null);
      return;
    }
    
    const validation = validateFile(selectedFile);
    if (!validation.isValid) {
      setError(validation.message);
      setFile(null);
      setValidationMessage({type: validation.type, message: validation.message});
      return;
    }
    
    setFile(selectedFile);
    setValidationMessage({type: validation.type, message: validation.message});
  };

  const handleDragEnter = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  }, []);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      handleFileChange(e.dataTransfer.files[0]);
    }
  }, [handleFileChange]);

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      handleFileChange(e.target.files[0]);
    }
  };

  const handleRemoveFile = () => {
    setFile(null);
    setError(null);
    setSuccess(null);
    setUploadProgress(0);
    setValidationMessage({type: null, message: null});
    // Reset file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const simulateProgress = () => {
    setUploadProgress(0);
    const interval = setInterval(() => {
      setUploadProgress((prevProgress) => {
        const newProgress = prevProgress + Math.random() * 10;
        return newProgress >= 90 ? 90 : newProgress; // Cap at 90% until actual completion
      });
    }, 300);
    return interval;
  };

  const onSubmit = async (data: UploadFormData) => {
    if (!file) {
      setError('Please select a file to upload');
      return;
    }

    if (!data.schoolId) {
      setError('Please select a school');
      return;
    }

    setError(null);
    setSuccess(null);
    setIsUploading(true);
    
    // Start progress simulation
    const progressInterval = simulateProgress();

    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('schoolId', data.schoolId);

      const response = await uploadExaminationFormatAction(formData);
      
      // Clear progress interval
      clearInterval(progressInterval);
      
      if (response.status === 'success') {
        setUploadProgress(100);
        setSuccess('Examination format uploaded successfully');
        setFile(null);
        setValidationMessage({type: null, message: null});
        if (fileInputRef.current) {
          fileInputRef.current.value = '';
        }
        if (onUploadSuccess) {
          onUploadSuccess(response.data);
        }
      } else {
        const errorMsg = typeof response.message === 'string' ? response.message : 'Upload failed';
        setError(errorMsg);
        if (onUploadError) {
          onUploadError(errorMsg);
        }
      }
    } catch (err) {
      clearInterval(progressInterval);
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      setError(errorMessage);
      if (onUploadError) {
        onUploadError(errorMessage);
      }
    } finally {
      setIsUploading(false);
    }
  };

  const getValidationIcon = () => {
    switch (validationMessage.type) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-amber-500" />;
      case 'error':
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      default:
        return null;
    }
  };

  return (
    <div className={`relative ${className || ''}`}>
      {/* AI-Inspired Header */}
      {!isReplacement && (
        <div className="relative mb-8">
          <div className="absolute inset-0 bg-gradient-to-r from-blue-600/10 via-purple-600/10 to-indigo-600/10 rounded-2xl blur-xl"></div>
          <div className="relative bg-white/80 backdrop-blur-sm border border-white/20 rounded-2xl p-6">
            <div className="flex items-center gap-3 mb-2">
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full blur-sm opacity-75"></div>
                <div className="relative bg-gradient-to-r from-blue-500 to-purple-600 p-2 rounded-full">
                  <Brain className="h-5 w-5 text-white" />
                </div>
              </div>
              <h3 className="text-xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
                AI-Powered Examination Format Upload
              </h3>
              <Sparkles className="h-4 w-4 text-purple-500 animate-pulse" />
            </div>
            <p className="text-sm text-gray-600 leading-relaxed">
              Upload your examination format and let our AI analyze and optimize it for the best learning experience.
            </p>
          </div>
        </div>
      )}

      {/* Enhanced Error/Success Messages */}
      {error && (
        <div className="mb-6 relative">
          <div className="absolute inset-0 bg-red-500/10 rounded-xl blur-sm"></div>
          <div className="relative bg-red-50/80 backdrop-blur-sm border border-red-200/50 rounded-xl p-4">
            <div className="flex items-center gap-3">
              <div className="bg-red-100 p-2 rounded-full">
                <AlertTriangle className="h-4 w-4 text-red-600" />
              </div>
              <p className="text-red-800 font-medium">{error}</p>
            </div>
          </div>
        </div>
      )}
      
      {success && (
        <div className="mb-6 relative">
          <div className="absolute inset-0 bg-green-500/10 rounded-xl blur-sm"></div>
          <div className="relative bg-green-50/80 backdrop-blur-sm border border-green-200/50 rounded-xl p-4">
            <div className="flex items-center gap-3">
              <div className="bg-green-100 p-2 rounded-full">
                <CheckCircle className="h-4 w-4 text-green-600" />
              </div>
              <p className="text-green-800 font-medium">{success}</p>
            </div>
          </div>
        </div>
      )}

      {/* Upload Form */}
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* School Selector with AI styling */}
        {!initialSchoolId && (
          <div className="relative">
            <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-purple-500/5 rounded-lg blur-sm"></div>
            <div className="relative bg-white/60 backdrop-blur-sm border border-white/30 rounded-lg p-3">
              <Controller
                name="schoolId"
                control={control}
                rules={{ required: 'Please select a school' }}
                render={({ field }) => (
                  <div className="space-y-1">
                    <label className="text-xs font-medium text-gray-700 flex items-center gap-1">
                      <div className="w-1 h-1 bg-blue-500 rounded-full"></div>
                      School Selection
                    </label>
                    <SchoolSelector
                      {...field}
                      label=""
                      error={errors.schoolId?.message}
                      disabled={isUploading}
                      className="text-sm"
                    />
                  </div>
                )}
              />
            </div>
          </div>
        )}

        {/* Enhanced File Upload Area */}
        <div className="relative">
          <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 via-purple-500/10 to-indigo-500/10 rounded-2xl blur-xl"></div>
          <div 
            className={`relative transition-all duration-500 ease-out transform ${
              isDragging 
                ? 'scale-105 bg-gradient-to-br from-blue-50 to-purple-50 border-2 border-dashed border-blue-400 shadow-2xl' 
                : file 
                ? 'bg-gradient-to-br from-green-50 to-emerald-50 border-2 border-dashed border-green-300 shadow-lg' 
                : 'bg-white/70 backdrop-blur-sm border-2 border-dashed border-gray-300 hover:border-blue-400 hover:bg-blue-50/50'
            } rounded-2xl p-8 cursor-pointer group`}
            onDragEnter={handleDragEnter}
            onDragLeave={handleDragLeave}
            onDragOver={handleDragOver}
            onDrop={handleDrop}
          >
            {!file ? (
              <div className="flex flex-col items-center justify-center py-6">
                {/* Animated Upload Icon */}
                <div className="relative mb-6">
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full blur-lg opacity-30 group-hover:opacity-50 transition-opacity duration-300"></div>
                  <div className="relative bg-gradient-to-r from-blue-500 to-purple-600 p-4 rounded-full transform group-hover:scale-110 transition-transform duration-300">
                    <Upload className="h-8 w-8 text-white animate-bounce" />
                  </div>
                  <div className="absolute -top-1 -right-1">
                    <Zap className="h-4 w-4 text-yellow-500 animate-pulse" />
                  </div>
                </div>
                
                <div className="text-center space-y-2">
                  <h4 className="text-base font-semibold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
                    Drop your examination format here
                  </h4>
                  <p className="text-xs text-gray-600 max-w-sm">
                    AI will analyze your PDF for optimal student engagement
                  </p>
                  <div className="flex items-center justify-center gap-1 text-xs text-gray-500">
                    <span className="bg-gray-100 px-1.5 py-0.5 rounded text-xs">PDF</span>
                    <span className="bg-gray-100 px-1.5 py-0.5 rounded text-xs">5MB</span>
                    <span className="bg-gradient-to-r from-blue-100 to-purple-100 px-1.5 py-0.5 rounded text-xs text-blue-700">AI</span>
                  </div>
                </div>
                
                <input
                  type="file"
                  id="file-upload"
                  className="hidden"
                  accept="application/pdf"
                  onChange={handleFileInputChange}
                  ref={fileInputRef}
                  disabled={isUploading}
                />
                
                <div className="mt-4">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => fileInputRef.current?.click()}
                    disabled={isUploading}
                    className="!w-auto bg-white/80 backdrop-blur-sm border border-blue-200 hover:border-blue-400 hover:bg-blue-50 transition-all duration-300 transform hover:scale-105 text-sm px-4 py-2"
                  >
                    <div className="flex items-center gap-1.5">
                      <File className="h-3.5 w-3.5" />
                      <span>Browse</span>
                      <Sparkles className="h-2.5 w-2.5 text-purple-500" />
                    </div>
                  </Button>
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                {/* Enhanced File Preview */}
                <div className="relative">
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-green-500/10 rounded-xl blur-sm"></div>
                  <div className="relative bg-white/90 backdrop-blur-sm p-4 rounded-xl border border-white/50 shadow-lg">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        <div className="relative">
                          <div className="absolute inset-0 bg-gradient-to-r from-blue-500 to-green-500 rounded-full blur-sm opacity-75"></div>
                          <div className="relative bg-gradient-to-r from-blue-500 to-green-500 p-3 rounded-full">
                            <File className="h-6 w-6 text-white" />
                          </div>
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="font-semibold text-gray-900 truncate">{file.name}</p>
                          <div className="flex items-center gap-3 mt-1">
                            <span className="text-sm text-gray-600">{(file.size / 1024).toFixed(1)} KB</span>
                            <span className="bg-green-100 text-green-700 text-xs px-2 py-1 rounded-full font-medium">
                              Ready for AI Analysis
                            </span>
                          </div>
                        </div>
                      </div>
                      
                      <button
                        type="button"
                        onClick={handleRemoveFile}
                        className="text-gray-400 hover:text-red-500 p-2 rounded-full hover:bg-red-50 transition-all duration-200"
                        disabled={isUploading}
                        aria-label="Remove file"
                      >
                        <X className="h-5 w-5" />
                      </button>
                    </div>
                  </div>
                </div>

                {/* Enhanced Validation Message */}
                {validationMessage.message && (
                  <div className={`flex items-center gap-3 p-3 rounded-xl ${
                    validationMessage.type === 'success' 
                      ? 'bg-green-50 border border-green-200 text-green-700' 
                      : validationMessage.type === 'warning' 
                      ? 'bg-amber-50 border border-amber-200 text-amber-700' 
                      : 'bg-red-50 border border-red-200 text-red-700'
                  }`}>
                    {getValidationIcon()}
                    <span className="text-sm font-medium">{validationMessage.message}</span>
                  </div>
                )}
                
                {/* Enhanced Upload Progress */}
                {isUploading && (
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <div className="flex items-center gap-2">
                        <Brain className="h-4 w-4 text-blue-600 animate-pulse" />
                        <span className="text-sm font-semibold text-gray-700">AI Processing...</span>
                      </div>
                      <span className="text-sm font-bold text-blue-600">{Math.round(uploadProgress)}%</span>
                    </div>
                    <div className="relative w-full bg-gray-200 rounded-full h-3 overflow-hidden">
                      <div className="absolute inset-0 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-full"></div>
                      <div 
                        className="bg-gradient-to-r from-blue-500 to-purple-600 h-3 rounded-full transition-all duration-500 ease-out relative overflow-hidden" 
                        style={{ width: `${uploadProgress}%` }}
                      >
                        <div className="absolute inset-0 bg-white/30 animate-pulse"></div>
                      </div>
                    </div>
                    <p className="text-xs text-gray-600 text-center">
                      Analyzing content structure and optimizing for learning outcomes...
                    </p>
                  </div>
                )}

                {/* Enhanced Upload Button */}
                <div className="flex justify-end pt-2">
                  <Button
                    type="submit"
                    variant="primary"
                    disabled={isUploading}
                    className="!w-auto bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 border-0 shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300"
                  >
                    {isUploading ? (
                      <div className="flex items-center gap-2">
                        <Loader2 className="h-4 w-4 animate-spin" />
                        <span>Processing with AI...</span>
                      </div>
                    ) : (
                      <div className="flex items-center gap-2">
                        <Zap className="h-4 w-4" />
                        <span>{isReplacement ? 'Replace & Enhance' : 'Upload & Enhance'}</span>
                        <Sparkles className="h-3 w-3" />
                      </div>
                    )}
                  </Button>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Compact Format Requirements */}
        <div className="relative">
          <div className="absolute inset-0 bg-gradient-to-r from-gray-500/5 to-blue-500/5 rounded-lg blur-sm"></div>
          <div className="relative bg-white/60 backdrop-blur-sm border border-white/30 rounded-lg p-3">
            <div className="flex items-center gap-2 mb-2">
              <div className="bg-gradient-to-r from-blue-500 to-purple-600 p-1 rounded-full">
                <CheckCircle className="h-2.5 w-2.5 text-white" />
              </div>
              <h4 className="text-xs font-semibold text-gray-800">Requirements</h4>
            </div>
            <div className="flex flex-wrap gap-2">
              <span className="inline-flex items-center gap-1 bg-blue-50 text-blue-700 text-xs px-2 py-1 rounded-full">
                <div className="w-1 h-1 bg-blue-500 rounded-full"></div>
                PDF only
              </span>
              <span className="inline-flex items-center gap-1 bg-purple-50 text-purple-700 text-xs px-2 py-1 rounded-full">
                <div className="w-1 h-1 bg-purple-500 rounded-full"></div>
                Max 5MB
              </span>
              <span className="inline-flex items-center gap-1 bg-green-50 text-green-700 text-xs px-2 py-1 rounded-full">
                <div className="w-1 h-1 bg-green-500 rounded-full"></div>
                Clear content
              </span>
              <span className="inline-flex items-center gap-1 bg-indigo-50 text-indigo-700 text-xs px-2 py-1 rounded-full">
                <div className="w-1 h-1 bg-indigo-500 rounded-full"></div>
                AI-enhanced
              </span>
            </div>
          </div>
        </div>
      </form>
    </div>
  );
};