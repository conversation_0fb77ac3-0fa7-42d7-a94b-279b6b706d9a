export enum EViewType {
  LISTING = 'listing',
  CREATE = 'create',
  REVIEW = 'review',
}

export enum ERoutes {
  HOME = '/',
  MANAGE_WORKSHEET = '/manage-worksheet',
  MANA<PERSON>_WORKSHEET_CREATE = `/manage-worksheet?type=${EViewType.CREATE}`,
  MANAGE_WORKSHEET_REVIEW = `/manage-worksheet?type=${EViewType.REVIEW}`,
  USERS_MANAGEMENT = '/users-management',
  TEACHER_MANAGEMENT = '/teacher-management',
  SCHOOLS = '/school-management',
}
