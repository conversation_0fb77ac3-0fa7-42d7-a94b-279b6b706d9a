import { withAuth } from 'next-auth/middleware';
import { NextResponse } from 'next/server';
import { EUserRole } from '@/config/enums/user';

export default withAuth(
  async function middleware(req) {
    const token = req.nextauth.token;
    const path = new URL(req.url).pathname;

    // Create a response object
    const response = NextResponse.next();

    // Set the custom header with the current path
    response.headers.set('x-current-path', path);

    // Check role-based access for protected routes
    if (token?.isLogin) {
      const userRole = token.role;

      // Check access to /users-management routes (ADMIN only)
      if (path.startsWith('/users-management') && userRole !== EUserRole.ADMIN) {
        return NextResponse.redirect(new URL('/', req.url));
      }

      // Check access to /manage-worksheet routes (TEACHER or SCHOOL_MANAGER)
      if (
        path.startsWith('/manage-worksheet') &&
        !(userRole === EUserRole.TEACHER || userRole === EUserRole.SCHOOL_MANAGER)
      ) {
        return NextResponse.redirect(new URL('/', req.url));
      }

      // Check access to /teacher-management routes (SCHOOL_MANAGER only)
      if (path.startsWith('/teacher-management') && userRole !== EUserRole.SCHOOL_MANAGER) {
        return NextResponse.redirect(new URL('/', req.url));
      }
    }

    // Log for debugging
    // console.log('Path:', path);
    // console.log('Token:', token);

    // Return the response
    return response;
  },
  {
    callbacks: {
      authorized: ({ req, token }) => {
        const path = req.nextUrl.pathname;

        // Allow access to /auth routes
        if (path.startsWith('/auth')) {
          return true;
        }

        // Only allow access if the token indicates the user is logged in
        return !!token?.isLogin;
      },
    },
  }
);

export const config = {
  // Matcher ignoring `/_next/` and `/api/`
  matcher: ['/((?!api|_next/static|_next/image|favicon.ico|assets|public).*)'],
};
